// Epoch Towns World Generation System

import {
  EpochTown,
  TownWorld,
  TerrainBlock,
  TownBuilding,
  BuildingBlock,
  Vector3D,
  TownGenetics,
  DEFAULT_TOWN_GENETICS,
  TOWN_BUILDING_TYPES,
  MILESTONE_TEMPLATES
} from '@/types/epochTowns'

export class WorldGenerator {
  private static readonly WORLD_SIZE = { width: 32, height: 8, depth: 32 }
  private static readonly NOISE_SCALE = 0.1
  
  // Generate a new town for a given month/generation
  static generateTown(generation: string, inheritedTraits: string[] = []): EpochTown {
    const townId = `town_${generation.replace(/\s+/g, '_').toLowerCase()}`
    const world = this.generateWorld(inheritedTraits)
    const genetics = this.generateGenetics(inheritedTraits)
    
    return {
      id: townId,
      name: `NanoVerse ${generation}`,
      generation,
      createdAt: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
      status: 'active',
      
      stats: {
        totalCitizens: 0,
        activeContributors: 0,
        wisdomLevel: 0,
        empathyLevel: 0,
        builderLevel: 0,
        curiosityLevel: 0,
        totalContributions: 0,
        milestonesUnlocked: 0
      },
      
      genetics,
      world,
      unlockedAreas: ['spawn_area'],
      buildings: this.generateInitialBuildings(world),
      milestones: this.generateMilestones(),
      events: [
        {
          id: `event_town_created_${Date.now()}`,
          type: 'milestone_reached' as const,
          title: 'Town Founded',
          description: `NanoVerse ${generation} has been established!`,


          timestamp: new Date(),
          involvedUsers: [],
          townImpact: {
            wisdomBonus: 10,
            empathyBonus: 5,
            builderBonus: 15,
            curiosityBonus: 10,
            collaborationBonus: 20
          },
          xpAwarded: 0,
          celebrationEffect: 'fireworks',
          duration: 5,
          isHistoricallySignificant: true,
          legacyValue: 100
        },
        {
          id: `event_welcome_${Date.now() + 1}`,
          type: 'legacy_created' as const,
          title: 'Welcome Citizens',
          description: 'Ready to start building your collaborative legacy?',


          timestamp: new Date(Date.now() + 1000),
          involvedUsers: [],
          townImpact: {
            wisdomBonus: 5,
            empathyBonus: 10,
            builderBonus: 5,
            curiosityBonus: 15,
            collaborationBonus: 10
          },
          xpAwarded: 0,
          celebrationEffect: 'sparkles',
          duration: 3,
          isHistoricallySignificant: false,
          legacyValue: 25
        }
      ],
      inheritedTraits
    }
  }
  
  // Generate the 3D world terrain
  private static generateWorld(inheritedTraits: string[]): TownWorld {
    const terrain: TerrainBlock[][][] = []
    
    // Initialize 3D array
    for (let x = 0; x < this.WORLD_SIZE.width; x++) {
      terrain[x] = []
      for (let y = 0; y < this.WORLD_SIZE.height; y++) {
        terrain[x][y] = []
        for (let z = 0; z < this.WORLD_SIZE.depth; z++) {
          terrain[x][y][z] = this.generateTerrainBlock(x, y, z, inheritedTraits)
        }
      }
    }
    
    // Add special features based on inherited traits
    this.addInheritedFeatures(terrain, inheritedTraits)
    
    return {
      size: this.WORLD_SIZE,
      terrain,
      spawnPoint: { x: 16, y: 4, z: 16 },
      townCenter: { x: 16, y: 4, z: 16 },
      importantLocations: {
        'spawn': { x: 16, y: 4, z: 16 },
        'north_gate': { x: 16, y: 4, z: 4 },
        'south_gate': { x: 16, y: 4, z: 28 },
        'east_gate': { x: 28, y: 4, z: 16 },
        'west_gate': { x: 4, y: 4, z: 16 }
      },
      environment: {
        skyColor: '#87CEEB',
        fogColor: '#B0E0E6',
        lightingIntensity: 0.8,
        timeOfDay: 'day'
      }
    }
  }
  
  // Generate individual terrain blocks
  private static generateTerrainBlock(x: number, y: number, z: number, inheritedTraits: string[]): TerrainBlock {
    const centerX = this.WORLD_SIZE.width / 2
    const centerZ = this.WORLD_SIZE.depth / 2
    const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (z - centerZ) ** 2)
    
    // Base height using simple noise
    const baseHeight = 3 + Math.sin(x * this.NOISE_SCALE) * Math.cos(z * this.NOISE_SCALE) * 2
    
    // Determine block type based on height and position
    if (y === 0) {
      return { type: 'stone', color: '#696969', height: 1, hasBuilding: false }
    }
    
    if (y <= baseHeight) {
      if (y === Math.floor(baseHeight)) {
        // Surface blocks
        if (distanceFromCenter < 3) {
          // Town center - special crystal blocks
          return { type: 'crystal', color: '#00FFFF', height: 1, hasBuilding: false }
        } else if (distanceFromCenter < 8) {
          // Inner town - grass
          return { type: 'grass', color: '#228B22', height: 1, hasBuilding: false }
        } else if (distanceFromCenter < 12) {
          // Outer town - mixed terrain
          return Math.random() > 0.7 
            ? { type: 'sand', color: '#F4A460', height: 1, hasBuilding: false }
            : { type: 'grass', color: '#32CD32', height: 1, hasBuilding: false }
        } else {
          // Wilderness
          return { type: 'grass', color: '#228B22', height: 1, hasBuilding: false }
        }
      } else {
        // Underground
        return { type: 'stone', color: '#708090', height: 1, hasBuilding: false }
      }
    }
    
    // Air/water blocks
    if (y <= 2 && distanceFromCenter > 15) {
      return { type: 'water', color: '#4682B4', height: 1, hasBuilding: false }
    }
    
    return { type: 'void', color: '#000000', height: 0, hasBuilding: false }
  }
  
  // Add special features based on inherited traits
  private static addInheritedFeatures(terrain: TerrainBlock[][][], inheritedTraits: string[]) {
    if (inheritedTraits.includes('ancient_wisdom')) {
      // Add wisdom crystals
      this.addWisdomCrystals(terrain)
    }
    
    if (inheritedTraits.includes('empathy_rich')) {
      // Add empathy gardens
      this.addEmpathyGardens(terrain)
    }
    
    if (inheritedTraits.includes('builder_generation')) {
      // Add construction materials
      this.addBuilderMaterials(terrain)
    }
  }
  
  private static addWisdomCrystals(terrain: TerrainBlock[][][]) {
    const crystalPositions = [
      { x: 8, y: 4, z: 8 },
      { x: 24, y: 4, z: 8 },
      { x: 8, y: 4, z: 24 },
      { x: 24, y: 4, z: 24 }
    ]
    
    crystalPositions.forEach(pos => {
      if (terrain[pos.x] && terrain[pos.x][pos.y] && terrain[pos.x][pos.y][pos.z]) {
        terrain[pos.x][pos.y][pos.z] = {
          type: 'crystal',
          color: '#9370DB',
          height: 2,
          hasBuilding: false
        }
      }
    })
  }
  
  private static addEmpathyGardens(terrain: TerrainBlock[][][]) {
    // Add flower-like blocks in circular patterns
    const gardenCenters = [
      { x: 12, z: 12 },
      { x: 20, z: 20 }
    ]
    
    gardenCenters.forEach(center => {
      for (let dx = -2; dx <= 2; dx++) {
        for (let dz = -2; dz <= 2; dz++) {
          const distance = Math.sqrt(dx * dx + dz * dz)
          if (distance <= 2) {
            const x = center.x + dx
            const z = center.z + dz
            const y = 4
            
            if (terrain[x] && terrain[x][y] && terrain[x][y][z]) {
              terrain[x][y][z] = {
                type: 'grass',
                color: '#FFB6C1',
                height: 1,
                hasBuilding: false
              }
            }
          }
        }
      }
    })
  }
  
  private static addBuilderMaterials(terrain: TerrainBlock[][][]) {
    // Add scattered building material blocks
    const materialPositions = [
      { x: 6, y: 4, z: 16 },
      { x: 26, y: 4, z: 16 },
      { x: 16, y: 4, z: 6 },
      { x: 16, y: 4, z: 26 }
    ]
    
    materialPositions.forEach(pos => {
      if (terrain[pos.x] && terrain[pos.x][pos.y] && terrain[pos.x][pos.y][pos.z]) {
        terrain[pos.x][pos.y][pos.z] = {
          type: 'wood',
          color: '#8B4513',
          height: 1,
          hasBuilding: false
        }
      }
    })
  }
  
  // Generate town genetics based on inherited traits
  private static generateGenetics(inheritedTraits: string[]): TownGenetics {
    const genetics = { ...DEFAULT_TOWN_GENETICS }
    
    // Apply inherited trait bonuses
    inheritedTraits.forEach(trait => {
      switch (trait) {
        case 'ancient_wisdom':
          genetics.traits.ancient_wisdom += 0.2
          break
        case 'empathy_rich':
          genetics.traits.empathy_rich += 0.3
          break
        case 'builder_generation':
          genetics.traits.builder_generation += 0.25
          break
        case 'hacker_minded':
          genetics.traits.hacker_minded += 0.2
          break
        case 'collaborative_spirit':
          genetics.traits.collaborative_spirit += 0.15
          break
        case 'curious_minds':
          genetics.traits.curious_minds += 0.2
          break
      }
    })
    
    // Ensure traits don't exceed 1.0
    Object.keys(genetics.traits).forEach(key => {
      genetics.traits[key as keyof typeof genetics.traits] = Math.min(
        genetics.traits[key as keyof typeof genetics.traits], 
        1.0
      )
    })
    
    // Generate DNA string
    genetics.dnaString = this.generateDNAString(genetics.traits)
    
    return genetics
  }
  
  private static generateDNAString(traits: TownGenetics['traits']): string {
    const traitCodes = {
      empathyRich: 'E',
      hackerMinded: 'H',
      builderGeneration: 'B',
      curiousMinds: 'C',
      collaborativeSpirit: 'S',
      innovativeThinking: 'I'
    }
    
    return Object.entries(traits)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 6)
      .map(([trait]) => traitCodes[trait as keyof typeof traitCodes])
      .join('')
  }
  
  // Generate initial buildings (just the spawn area)
  private static generateInitialBuildings(world: TownWorld): TownBuilding[] {
    return [
      {
        id: 'spawn_monument',
        name: 'Genesis Monument',
        type: 'collaboration_center',
        position: world.townCenter,
        size: { x: 3, y: 3, z: 3 },
        level: 1,
        capacity: 50,
        currentOccupancy: 0,
        blocks: this.generateMonumentBlocks(),
        color: '#FFD700',
        glowIntensity: 0.5,
        purpose: 'Welcome new citizens and coordinate town activities',
        unlockedFeatures: ['chat', 'citizen_directory'],
        requiredContributions: 0,
        activeUsers: [],
        recentActivities: []
      }
    ]
  }
  
  private static generateMonumentBlocks(): BuildingBlock[] {
    const blocks = []
    
    // Create a simple monument structure
    for (let x = -1; x <= 1; x++) {
      for (let z = -1; z <= 1; z++) {
        for (let y = 0; y < 3; y++) {
          blocks.push({
            position: { x, y, z },
            type: 'monument',
            color: y === 2 ? '#FFD700' : '#DAA520',
            material: (y === 2 ? 'crystalline' : 'enhanced') as 'basic' | 'enhanced' | 'crystalline' | 'energy'
          })
        }
      }
    }
    
    return blocks
  }
  
  // Generate milestone templates for the town
  private static generateMilestones() {
    return MILESTONE_TEMPLATES.map((template, index) => ({
      id: `milestone_${index}`,
      name: template.name,
      description: template.description,
      type: template.type,
      requiredContributions: template.requiredContributions,
      requiredUsers: template.requiredUsers,
      requiredActions: [],
      currentProgress: 0,
      isUnlocked: false,
      rewards: template.rewards,
      icon: this.getMilestoneIcon(template.type),
      color: this.getMilestoneColor(template.type),
      celebrationEffect: 'sparkles'
    }))
  }
  
  private static getMilestoneIcon(type: string): string {
    const icons = {
      wisdom: '🧠',
      empathy: '❤️',
      building: '🏗️',
      curiosity: '🔍',
      collaboration: '🤝'
    }
    return icons[type as keyof typeof icons] || '⭐'
  }
  
  private static getMilestoneColor(type: string): string {
    const colors = {
      wisdom: '#4A90E2',
      empathy: '#FFB6C1',
      building: '#50C878',
      curiosity: '#9370DB',
      collaboration: '#FFD700'
    }
    return colors[type as keyof typeof colors] || '#00FFFF'
  }
}

// Utility functions for town management
export class TownManager {
  // Add a new building to the town
  static addBuilding(town: EpochTown, buildingType: keyof typeof TOWN_BUILDING_TYPES, position: Vector3D): TownBuilding {
    const config = TOWN_BUILDING_TYPES[buildingType]
    const buildingId = `${buildingType}_${Date.now()}`
    
    const building: TownBuilding = {
      id: buildingId,
      name: config.name,
      type: buildingType,
      position,
      size: this.getBuildingSize(buildingType),
      level: 1,
      capacity: config.capacity,
      currentOccupancy: 0,
      blocks: this.generateBuildingBlocks(buildingType),
      color: config.color,
      glowIntensity: 0.3,
      purpose: config.description,
      unlockedFeatures: this.getInitialFeatures(buildingType),
      requiredContributions: 0,
      activeUsers: [],
      recentActivities: []
    }
    
    return building
  }
  
  private static getBuildingSize(type: keyof typeof TOWN_BUILDING_TYPES): Vector3D {
    const sizes = {
      library: { x: 5, y: 4, z: 5 },
      tech_hub: { x: 4, y: 3, z: 4 },
      empathy_garden: { x: 6, y: 2, z: 6 },
      maker_space: { x: 4, y: 3, z: 4 },
      wisdom_tower: { x: 3, y: 8, z: 3 },
      collaboration_center: { x: 7, y: 3, z: 7 }
    }
    return sizes[type]
  }
  
  private static generateBuildingBlocks(type: keyof typeof TOWN_BUILDING_TYPES) {
    const size = this.getBuildingSize(type)
    const blocks = []
    const config = TOWN_BUILDING_TYPES[type]
    
    // Generate basic building structure
    for (let x = 0; x < size.x; x++) {
      for (let z = 0; z < size.z; z++) {
        for (let y = 0; y < size.y; y++) {
          // Only create walls and special features
          if (x === 0 || x === size.x - 1 || z === 0 || z === size.z - 1 || y === 0 || y === size.y - 1) {
            blocks.push({
              position: { x: x - Math.floor(size.x / 2), y, z: z - Math.floor(size.z / 2) },
              type: 'building',
              color: config.color,
              material: 'enhanced' as const
            })
          }
        }
      }
    }
    
    return blocks
  }
  
  private static getInitialFeatures(type: keyof typeof TOWN_BUILDING_TYPES): string[] {
    const features = {
      library: ['reading', 'research', 'knowledge_sharing'],
      tech_hub: ['coding', 'innovation', 'project_collaboration'],
      empathy_garden: ['meditation', 'emotional_support', 'peer_counseling'],
      maker_space: ['building', 'prototyping', 'hands_on_learning'],
      wisdom_tower: ['advanced_learning', 'mentorship', 'deep_thinking'],
      collaboration_center: ['team_formation', 'group_projects', 'community_events']
    }
    return features[type] || []
  }
  
  // Update town stats based on contributions
  static updateTownStats(town: EpochTown, contributions: { [key: string]: number }) {
    town.stats.wisdomLevel = Math.min(100, town.stats.wisdomLevel + (contributions.wisdom || 0))
    town.stats.empathyLevel = Math.min(100, town.stats.empathyLevel + (contributions.empathy || 0))
    town.stats.builderLevel = Math.min(100, town.stats.builderLevel + (contributions.builder || 0))
    town.stats.curiosityLevel = Math.min(100, town.stats.curiosityLevel + (contributions.curiosity || 0))
    town.stats.totalContributions += Object.values(contributions).reduce((sum, val) => sum + val, 0)
    
    // Check for milestone unlocks
    this.checkMilestones(town)
  }
  
  private static checkMilestones(town: EpochTown) {
    town.milestones.forEach(milestone => {
      if (!milestone.isUnlocked) {
        const meetsRequirements = 
          town.stats.totalContributions >= milestone.requiredContributions &&
          town.stats.totalCitizens >= milestone.requiredUsers
        
        if (meetsRequirements) {
          milestone.isUnlocked = true
          milestone.unlockedAt = new Date()
          town.stats.milestonesUnlocked++
          
          // Apply milestone rewards
          if (milestone.rewards.newBuilding) {
            // Logic to unlock new building type
          }
        }
      }
    })
  }


}
