import { 
  TownGenetics, 
  LegacyTrait, 
  TownStats,
  EpochTown 
} from '@/types/epochTowns'

// Base trait definitions with their characteristics
export const BASE_TRAITS = {
  empathy_rich: {
    name: 'Empathy <PERSON>',
    description: 'A generation that prioritized emotional intelligence and mutual support',
    color: '#FFB6C1',
    icon: '❤️',
    baseStrength: 0.3,
    maxStrength: 1.0,
    requirements: {
      empathyLevel: 80,
      totalContributions: 500,
      mentorships: 10
    },
    effects: {
      empathyBonus: 0.2,
      collaborationBonus: 0.15,
      socialCohesion: 0.25
    }
  },
  ancient_wisdom: {
    name: 'Ancient Wisdom',
    description: 'Keepers of knowledge who valued learning above all',
    color: '#4A90E2',
    icon: '🧠',
    baseStrength: 0.25,
    maxStrength: 1.0,
    requirements: {
      wisdomLevel: 90,
      totalContributions: 800,
      knowledgeSharing: 20
    },
    effects: {
      wisdomBonus: 0.3,
      learningSpeed: 0.2,
      mentorEffectiveness: 0.25
    }
  },
  builder_generation: {
    name: '<PERSON>uilder Generation',
    description: 'Creators who built lasting structures and systems',
    color: '#50C878',
    icon: '🏗️',
    baseStrength: 0.3,
    maxStrength: 1.0,
    requirements: {
      builderLevel: 85,
      totalContributions: 600,
      structuresBuilt: 15
    },
    effects: {
      builderBonus: 0.25,
      constructionSpeed: 0.3,
      resourceEfficiency: 0.2
    }
  },
  curious_minds: {
    name: 'Curious Minds',
    description: 'Explorers who never stopped asking questions',
    color: '#9370DB',
    icon: '🔍',
    baseStrength: 0.2,
    maxStrength: 1.0,
    requirements: {
      curiosityLevel: 75,
      totalContributions: 400,
      explorationPoints: 25
    },
    effects: {
      curiosityBonus: 0.2,
      discoveryRate: 0.25,
      innovationSpeed: 0.15
    }
  },
  collaborative_spirit: {
    name: 'Collaborative Spirit',
    description: 'A generation that achieved greatness through cooperation',
    color: '#FFD700',
    icon: '🤝',
    baseStrength: 0.25,
    maxStrength: 1.0,
    requirements: {
      collaborationLevel: 80,
      totalContributions: 1000,
      teamProjects: 12
    },
    effects: {
      collaborationBonus: 0.3,
      teamEfficiency: 0.25,
      socialHarmony: 0.2
    }
  },
  hacker_minded: {
    name: 'Hacker Minded',
    description: 'Technical innovators who pushed boundaries',
    color: '#00FFFF',
    icon: '💻',
    baseStrength: 0.2,
    maxStrength: 1.0,
    requirements: {
      techLevel: 70,
      totalContributions: 350,
      innovationsCreated: 8
    },
    effects: {
      techBonus: 0.25,
      problemSolvingSpeed: 0.2,
      systemOptimization: 0.15
    }
  }
} as const

export type TraitType = keyof typeof BASE_TRAITS

// DNA encoding system - each trait gets a letter
const DNA_ENCODING: Record<TraitType, string> = {
  empathy_rich: 'E',
  ancient_wisdom: 'W',
  builder_generation: 'B',
  curious_minds: 'C',
  collaborative_spirit: 'S',
  hacker_minded: 'H'
}

export class GeneticsEngine {
  /**
   * Generate DNA string from trait strengths
   */
  static generateDNAString(traits: Record<TraitType, number>): string {
    return Object.entries(traits)
      .sort(([, a], [, b]) => b - a) // Sort by strength descending
      .slice(0, 6) // Take top 6 traits
      .map(([trait]) => DNA_ENCODING[trait as TraitType])
      .join('')
  }

  /**
   * Parse DNA string back to trait information
   */
  static parseDNAString(dnaString: string): Partial<Record<TraitType, number>> {
    const traits: Partial<Record<TraitType, number>> = {}
    const reverseEncoding = Object.fromEntries(
      Object.entries(DNA_ENCODING).map(([trait, letter]) => [letter, trait])
    )

    for (let i = 0; i < dnaString.length; i++) {
      const letter = dnaString[i]
      const trait = reverseEncoding[letter] as TraitType
      if (trait) {
        // Strength decreases with position in DNA string
        traits[trait] = Math.max(0.1, 1.0 - (i * 0.15))
      }
    }

    return traits
  }

  /**
   * Calculate trait strengths based on town statistics
   */
  static calculateTraitStrengths(stats: TownStats, achievements: string[]): Record<TraitType, number> {
    const traits: Record<TraitType, number> = {
      empathy_rich: 0,
      ancient_wisdom: 0,
      builder_generation: 0,
      curious_minds: 0,
      collaborative_spirit: 0,
      hacker_minded: 0
    }

    // Calculate base strengths from stats
    traits.empathy_rich = Math.min(1.0, (stats.empathyLevel / 100) * 0.8 + 
      (stats.totalContributions / 1000) * 0.2)
    
    traits.ancient_wisdom = Math.min(1.0, (stats.wisdomLevel / 100) * 0.9 + 
      (stats.milestonesUnlocked / 10) * 0.1)
    
    traits.builder_generation = Math.min(1.0, (stats.builderLevel / 100) * 0.8 + 
      (stats.totalContributions / 800) * 0.2)
    
    traits.curious_minds = Math.min(1.0, (stats.curiosityLevel / 100) * 0.7 + 
      (stats.totalContributions / 600) * 0.3)
    
    traits.collaborative_spirit = Math.min(1.0, 
      ((stats.empathyLevel + stats.wisdomLevel + stats.builderLevel + stats.curiosityLevel) / 400) * 0.6 +
      (stats.totalContributions / 1200) * 0.4)
    
    traits.hacker_minded = Math.min(1.0, 
      (stats.curiosityLevel / 100) * 0.4 + 
      (stats.builderLevel / 100) * 0.3 +
      (stats.totalContributions / 500) * 0.3)

    // Boost traits based on achievements
    achievements.forEach(achievement => {
      switch (achievement) {
        case 'empathy_champion':
          traits.empathy_rich += 0.15
          break
        case 'wisdom_keeper':
          traits.ancient_wisdom += 0.2
          break
        case 'master_builder':
          traits.builder_generation += 0.15
          break
        case 'curious_explorer':
          traits.curious_minds += 0.1
          break
        case 'collaboration_star':
          traits.collaborative_spirit += 0.2
          break
        case 'tech_innovator':
          traits.hacker_minded += 0.15
          break
      }
    })

    // Normalize to ensure no trait exceeds 1.0
    Object.keys(traits).forEach(trait => {
      traits[trait as TraitType] = Math.min(1.0, traits[trait as TraitType])
    })

    return traits
  }

  /**
   * Generate genetics for a new town based on previous generation
   */
  static generateTownGenetics(
    previousTown?: EpochTown,
    currentStats?: TownStats,
    achievements: string[] = []
  ): TownGenetics {
    let traits: Record<TraitType, number>

    if (currentStats) {
      // Calculate traits from current town performance
      traits = this.calculateTraitStrengths(currentStats, achievements)
    } else {
      // Generate base traits for a new town
      traits = {
        empathy_rich: Math.random() * 0.4 + 0.1,
        ancient_wisdom: Math.random() * 0.4 + 0.1,
        builder_generation: Math.random() * 0.4 + 0.1,
        curious_minds: Math.random() * 0.4 + 0.1,
        collaborative_spirit: Math.random() * 0.4 + 0.1,
        hacker_minded: Math.random() * 0.4 + 0.1
      }
    }

    // Apply inheritance from previous generation
    if (previousTown) {
      traits = this.applyInheritance(traits, previousTown.genetics)
    }

    const dnaString = this.generateDNAString(traits)
    const legacyTraits = this.generateLegacyTraits(traits, achievements)

    // Debug logging
    console.log('Generated genetics:', {
      traits,
      dnaString,
      legacyTraits: legacyTraits.length,
      generation: previousTown ? previousTown.genetics.generation + 1 : 1
    })

    return {
      traits,
      dnaString,
      legacyTraits,
      generation: previousTown ? previousTown.genetics.generation + 1 : 1,
      parentDNA: previousTown?.genetics.dnaString,
      mutationRate: 0.1,
      stabilityIndex: this.calculateStabilityIndex(traits)
    }
  }

  /**
   * Apply genetic inheritance from previous generation
   */
  static applyInheritance(
    currentTraits: Record<TraitType, number>,
    parentGenetics: TownGenetics
  ): Record<TraitType, number> {
    const inheritedTraits = { ...currentTraits }
    const inheritanceStrength = 0.3 // 30% inheritance from parent

    Object.keys(parentGenetics.traits).forEach(traitKey => {
      const trait = traitKey as TraitType
      const parentStrength = parentGenetics.traits[trait]
      const currentStrength = inheritedTraits[trait]

      // Blend parent and current traits
      inheritedTraits[trait] = Math.min(1.0, 
        currentStrength * (1 - inheritanceStrength) + 
        parentStrength * inheritanceStrength +
        (Math.random() - 0.5) * parentGenetics.mutationRate // Add mutation
      )
    })

    return inheritedTraits
  }

  /**
   * Generate legacy traits that can be passed to future generations
   */
  static generateLegacyTraits(
    traits: Record<TraitType, number>,
    achievements: string[]
  ): LegacyTrait[] {
    const legacyTraits: LegacyTrait[] = []

    Object.entries(traits).forEach(([traitKey, strength]) => {
      const trait = traitKey as TraitType
      const traitDef = BASE_TRAITS[trait]

      // Only create legacy traits for strong traits (>0.7)
      if (strength > 0.7) {
        legacyTraits.push({
          id: trait,
          name: traitDef.name,
          description: traitDef.description,
          strength,
          earnedBy: this.getTraitEarnedBy(trait, achievements),
          icon: traitDef.icon,
          color: traitDef.color
        })
      }
    })

    return legacyTraits.sort((a, b) => b.strength - a.strength)
  }

  /**
   * Calculate genetic stability index (0-1, higher is more stable)
   */
  static calculateStabilityIndex(traits: Record<TraitType, number>): number {
    const values = Object.values(traits)
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
    
    // Lower variance = higher stability
    return Math.max(0, 1 - variance * 2)
  }

  /**
   * Determine how a trait was earned
   */
  static getTraitEarnedBy(trait: TraitType, achievements: string[]): 'empathy' | 'building' | 'mentoring' | 'curiosity' | 'collaboration' {
    const traitAchievementMap: Record<TraitType, 'empathy' | 'building' | 'mentoring' | 'curiosity' | 'collaboration'> = {
      empathy_rich: 'empathy',
      ancient_wisdom: 'mentoring',
      builder_generation: 'building',
      curious_minds: 'curiosity',
      collaborative_spirit: 'collaboration',
      hacker_minded: 'curiosity'
    }

    return traitAchievementMap[trait] || 'collaboration'
  }

  /**
   * Predict future trait evolution based on current trends
   */
  static predictTraitEvolution(
    currentGenetics: TownGenetics,
    currentStats: TownStats,
    generationsAhead: number = 1
  ): Record<TraitType, number> {
    const currentTraits = { ...currentGenetics.traits }
    const statInfluence = this.calculateTraitStrengths(currentStats, [])

    // Simulate evolution over multiple generations
    for (let gen = 0; gen < generationsAhead; gen++) {
      Object.keys(currentTraits).forEach(traitKey => {
        const trait = traitKey as TraitType
        const currentStrength = currentTraits[trait]
        const statInfluenceStrength = statInfluence[trait]
        
        // Traits evolve toward stat influence with some randomness
        const evolutionDirection = statInfluenceStrength - currentStrength
        const evolutionRate = 0.2 // 20% change per generation
        const randomFactor = (Math.random() - 0.5) * 0.1 // ±5% random change
        
        currentTraits[trait] = Math.max(0, Math.min(1.0, 
          currentStrength + (evolutionDirection * evolutionRate) + randomFactor
        ))
      })
    }

    return currentTraits
  }

  /**
   * Generate trait recommendations for improving specific aspects
   */
  static generateTraitRecommendations(
    currentGenetics: TownGenetics,
    targetTraits: Partial<Record<TraitType, number>>
  ): Array<{
    trait: TraitType
    currentStrength: number
    targetStrength: number
    recommendations: string[]
  }> {
    const recommendations: Array<{
      trait: TraitType
      currentStrength: number
      targetStrength: number
      recommendations: string[]
    }> = []

    Object.entries(targetTraits).forEach(([traitKey, targetStrength]) => {
      const trait = traitKey as TraitType
      const currentStrength = currentGenetics.traits[trait] || 0
      const traitDef = BASE_TRAITS[trait]

      if (targetStrength && targetStrength > currentStrength) {
        const actionRecommendations: string[] = []

        // Generate specific recommendations based on trait requirements
        Object.entries(traitDef.requirements).forEach(([requirement, value]) => {
          switch (requirement) {
            case 'empathyLevel':
              actionRecommendations.push(`Focus on empathy-building activities (target: ${value})`)
              break
            case 'wisdomLevel':
              actionRecommendations.push(`Increase knowledge sharing and learning (target: ${value})`)
              break
            case 'builderLevel':
              actionRecommendations.push(`Engage in more construction projects (target: ${value})`)
              break
            case 'curiosityLevel':
              actionRecommendations.push(`Explore new areas and ask questions (target: ${value})`)
              break
            case 'totalContributions':
              actionRecommendations.push(`Increase overall community contributions (target: ${value})`)
              break
            case 'mentorships':
              actionRecommendations.push(`Mentor more community members (target: ${value})`)
              break
          }
        })

        recommendations.push({
          trait,
          currentStrength,
          targetStrength,
          recommendations: actionRecommendations
        })
      }
    })

    return recommendations.sort((a, b) => (b.targetStrength - b.currentStrength) - (a.targetStrength - a.currentStrength))
  }
}

export default GeneticsEngine
