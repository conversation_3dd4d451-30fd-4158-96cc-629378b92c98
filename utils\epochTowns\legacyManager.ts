import { 
  EpochTown, 
  TownGenetics, 
  LegacyTrait, 
  TownStats,
  TownMilestone,
  TownBuilding,
  TownEvent 
} from '@/types/epochTowns'
import { GeneticsEngine, TraitType, BASE_TRAITS } from './geneticsEngine'

export interface LegacyTransition {
  id: string
  fromTown: EpochTown
  toTown: EpochTown
  transitionDate: Date
  inheritedTraits: string[]
  newFeatures: string[]
  preservedBuildings: TownBuilding[]
  legacyEvents: TownEvent[]
  transitionScore: number
}

export interface GenerationSummary {
  townId: string
  townName: string
  generation: string
  duration: number // days
  finalStats: TownStats
  achievements: string[]
  legacyTraits: LegacyTrait[]
  notableEvents: TownEvent[]
  citizenCount: number
  contributionTotal: number
  milestonesReached: number
  culturalImpact: number
}

export class LegacyManager {
  /**
   * Create a new town generation based on the previous town's legacy
   */
  static createNextGeneration(
    previousTown: EpochTown,
    newGenerationName: string,
    newCitizens: string[] = []
  ): EpochTown {
    // Generate genetics based on previous town's performance
    const newGenetics = GeneticsEngine.generateTownGenetics(
      previousTown,
      previousTown.stats,
      this.extractAchievements(previousTown)
    )

    // Determine inherited traits
    const inheritedTraits = this.determineInheritedTraits(previousTown.genetics)

    // Create base stats with inheritance bonuses
    const baseStats = this.calculateInheritedStats(previousTown.stats, newGenetics)

    // Preserve significant buildings
    const preservedBuildings = this.preserveBuildings(previousTown.buildings)

    // Generate starting milestones based on legacy
    const startingMilestones = this.generateStartingMilestones(newGenetics, inheritedTraits)

    const newTown: EpochTown = {
      id: `town_${Date.now()}`,
      name: newGenerationName,
      generation: newGenerationName,
      status: 'active',
      createdAt: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      genetics: newGenetics,
      inheritedTraits,
      stats: baseStats,
      milestones: startingMilestones,
      unlockedAreas: ['central_plaza', 'learning_district'],
      buildings: preservedBuildings,
      world: this.generateInheritedWorld(previousTown.world, newGenetics),
      events: []
    }

    return newTown
  }

  /**
   * Archive a completed town generation
   */
  static archiveTownGeneration(town: EpochTown): GenerationSummary {
    const duration = Math.floor(
      (new Date().getTime() - town.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    )

    const summary: GenerationSummary = {
      townId: town.id,
      townName: town.name,
      generation: town.generation,
      duration,
      finalStats: { ...town.stats },
      achievements: this.extractAchievements(town),
      legacyTraits: town.genetics.legacyTraits,
      notableEvents: town.events.filter(event => event.isHistoricallySignificant),
      citizenCount: town.stats.totalCitizens,
      contributionTotal: town.stats.totalContributions,
      milestonesReached: town.stats.milestonesUnlocked,
      culturalImpact: this.calculateCulturalImpact(town)
    }

    return summary
  }

  /**
   * Calculate the transition score between generations
   */
  static calculateTransitionScore(previousTown: EpochTown, newTown: EpochTown): number {
    let score = 0

    // Base score from previous town's achievements
    score += Math.min(50, previousTown.stats.totalContributions / 20)
    score += Math.min(20, previousTown.stats.milestonesUnlocked * 4)
    score += Math.min(15, previousTown.genetics.legacyTraits.length * 3)

    // Bonus for genetic diversity
    const geneticDiversity = this.calculateGeneticDiversity(newTown.genetics)
    score += geneticDiversity * 10

    // Bonus for preserved buildings
    const preservedBuildings = newTown.buildings.filter(b => b.isLegacy).length
    score += Math.min(10, preservedBuildings * 2)

    // Bonus for inherited traits
    score += Math.min(5, newTown.inheritedTraits.length)

    return Math.min(100, score)
  }

  /**
   * Determine which traits are inherited by the next generation
   */
  static determineInheritedTraits(genetics: TownGenetics): string[] {
    const inheritedTraits: string[] = []

    // Strong legacy traits (>0.8) are always inherited
    genetics.legacyTraits.forEach(trait => {
      if (trait.strength > 0.8) {
        inheritedTraits.push(trait.id)
      }
    })

    // Medium traits (0.6-0.8) have a chance to be inherited
    genetics.legacyTraits.forEach(trait => {
      if (trait.strength > 0.6 && trait.strength <= 0.8) {
        if (Math.random() < trait.strength) {
          inheritedTraits.push(trait.id)
        }
      }
    })

    return inheritedTraits
  }

  /**
   * Calculate starting stats for new generation with inheritance bonuses
   */
  static calculateInheritedStats(previousStats: TownStats, newGenetics: TownGenetics): TownStats {
    const baseStats: TownStats = {
      totalCitizens: 0,
      activeContributors: 0,
      wisdomLevel: 10,
      empathyLevel: 10,
      builderLevel: 10,
      curiosityLevel: 10,
      totalContributions: 0,
      milestonesUnlocked: 0
    }

    // Apply inheritance bonuses based on genetics
    Object.entries(newGenetics.traits).forEach(([traitKey, strength]) => {
      const trait = traitKey as TraitType
      const traitDef = BASE_TRAITS[trait]

      const effects = traitDef.effects as any
      if (effects.empathyBonus) {
        baseStats.empathyLevel += Math.floor(strength * effects.empathyBonus * 50)
      }
      if (effects.wisdomBonus) {
        baseStats.wisdomLevel += Math.floor(strength * effects.wisdomBonus * 50)
      }
      if (effects.builderBonus) {
        baseStats.builderLevel += Math.floor(strength * effects.builderBonus * 50)
      }
      if (effects.curiosityBonus) {
        baseStats.curiosityLevel += Math.floor(strength * effects.curiosityBonus * 50)
      }
    })

    // Ensure stats don't exceed reasonable starting values
    baseStats.wisdomLevel = Math.min(30, baseStats.wisdomLevel)
    baseStats.empathyLevel = Math.min(30, baseStats.empathyLevel)
    baseStats.builderLevel = Math.min(30, baseStats.builderLevel)
    baseStats.curiosityLevel = Math.min(30, baseStats.curiosityLevel)

    return baseStats
  }

  /**
   * Preserve significant buildings from previous generation
   */
  static preserveBuildings(previousBuildings: TownBuilding[]): TownBuilding[] {
    const preservedBuildings: TownBuilding[] = []

    previousBuildings.forEach(building => {
      // Preserve buildings that reached high levels or have special significance
      if (building.level >= 3 || building.isSpecial) {
        const preservedBuilding: TownBuilding = {
          ...building,
          id: `legacy_${building.id}`,
          isLegacy: true,
          level: Math.max(1, building.level - 1), // Slight degradation
          description: `${building.description} (Legacy from previous generation)`
        }
        preservedBuildings.push(preservedBuilding)
      }
    })

    return preservedBuildings
  }

  /**
   * Generate starting milestones based on inherited genetics
   */
  static generateStartingMilestones(genetics: TownGenetics, inheritedTraits: string[]): TownMilestone[] {
    const milestones: TownMilestone[] = []

    // Base milestones that all towns start with
    milestones.push({
      id: 'first_citizen',
      name: 'Welcome the First Citizen',
      description: 'A new generation begins with its first member',
      type: 'collaboration',
      requiredContributions: 0,
      requiredUsers: 1,
      requiredActions: [],
      rewards: { townBonus: 'empathy_boost' },
      isUnlocked: false,
      currentProgress: 0,
      icon: '👋',
      color: '#4A90E2',
      celebrationEffect: 'confetti'
    })

    // Add inherited milestone bonuses
    if (inheritedTraits.includes('empathy_rich')) {
      milestones.push({
        id: 'inherited_empathy',
        name: 'Inherited Empathy',
        description: 'Your generation begins with the gift of emotional intelligence',
        type: 'empathy',
        requiredContributions: 0,
        requiredUsers: 0,
        requiredActions: [],
        rewards: { townBonus: 'empathy_boost' },
        isUnlocked: true,
        currentProgress: 100,
        unlockedAt: new Date(),
        icon: '💝',
        color: '#E74C3C',
        celebrationEffect: 'hearts'
      })
    }

    if (inheritedTraits.includes('ancient_wisdom')) {
      milestones.push({
        id: 'inherited_wisdom',
        name: 'Ancient Knowledge',
        description: 'The wisdom of previous generations flows through your town',
        type: 'wisdom',
        requiredContributions: 0,
        requiredUsers: 0,
        requiredActions: [],
        rewards: { townBonus: 'wisdom_boost' },
        isUnlocked: true,
        currentProgress: 100,
        unlockedAt: new Date(),
        icon: '📚',
        color: '#9B59B6',
        celebrationEffect: 'sparkles'
      })
    }

    if (inheritedTraits.includes('builder_generation')) {
      milestones.push({
        id: 'inherited_building',
        name: 'Master Builders Legacy',
        description: 'Your generation inherits advanced construction knowledge',
        type: 'building',
        requiredContributions: 0,
        requiredUsers: 0,
        requiredActions: [],
        rewards: { townBonus: 'builder_boost' },
        isUnlocked: true,
        currentProgress: 100,
        unlockedAt: new Date(),
        icon: '🏗️',
        color: '#F39C12',
        celebrationEffect: 'construction'
      })
    }

    return milestones
  }

  /**
   * Generate world features based on inherited genetics
   */
  static generateInheritedWorld(previousWorld: any, genetics: TownGenetics): any {
    // This would integrate with the world generator to create terrain
    // and features that reflect the genetic inheritance
    const inheritedWorld = {
      ...previousWorld,
      inheritedFeatures: [],
      geneticInfluences: genetics.traits
    }

    // Add special terrain features based on strong traits
    Object.entries(genetics.traits).forEach(([trait, strength]) => {
      if (strength > 0.7) {
        switch (trait) {
          case 'empathy_rich':
            inheritedWorld.inheritedFeatures.push({
              type: 'empathy_garden',
              name: 'Garden of Understanding',
              description: 'A peaceful space that promotes emotional growth'
            })
            break
          case 'ancient_wisdom':
            inheritedWorld.inheritedFeatures.push({
              type: 'wisdom_monument',
              name: 'Monument of Knowledge',
              description: 'Ancient stones inscribed with the wisdom of previous generations'
            })
            break
          case 'builder_generation':
            inheritedWorld.inheritedFeatures.push({
              type: 'construction_site',
              name: 'Master Builder Workshop',
              description: 'Advanced tools and blueprints left by master builders'
            })
            break
        }
      }
    })

    return inheritedWorld
  }

  /**
   * Extract achievements from a town's history
   */
  static extractAchievements(town: EpochTown): string[] {
    const achievements: string[] = []

    // Extract from milestones
    town.milestones.forEach(milestone => {
      if (milestone.isUnlocked) {
        achievements.push(milestone.id)
      }
    })

    // Extract from stats
    if (town.stats.empathyLevel >= 80) achievements.push('empathy_champion')
    if (town.stats.wisdomLevel >= 90) achievements.push('wisdom_keeper')
    if (town.stats.builderLevel >= 85) achievements.push('master_builder')
    if (town.stats.curiosityLevel >= 75) achievements.push('curious_explorer')
    if (town.stats.totalContributions >= 1000) achievements.push('collaboration_star')
    if (town.stats.milestonesUnlocked >= 5) achievements.push('milestone_master')

    return achievements
  }

  /**
   * Calculate cultural impact score
   */
  static calculateCulturalImpact(town: EpochTown): number {
    let impact = 0

    // Base impact from contributions and citizens
    impact += Math.min(40, town.stats.totalContributions / 25)
    impact += Math.min(20, town.stats.totalCitizens * 2)
    impact += Math.min(20, town.stats.milestonesUnlocked * 4)

    // Bonus for genetic diversity
    const traitCount = Object.values(town.genetics.traits).filter(strength => strength > 0.5).length
    impact += Math.min(10, traitCount * 2)

    // Bonus for legacy traits
    impact += Math.min(10, town.genetics.legacyTraits.length * 2)

    return Math.min(100, impact)
  }

  /**
   * Calculate genetic diversity score
   */
  static calculateGeneticDiversity(genetics: TownGenetics): number {
    const traitValues = Object.values(genetics.traits)
    const mean = traitValues.reduce((sum, val) => sum + val, 0) / traitValues.length
    const variance = traitValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / traitValues.length
    
    // Higher variance = higher diversity (opposite of stability)
    return Math.min(1, variance * 3)
  }

  /**
   * Generate legacy report for a completed generation
   */
  static generateLegacyReport(town: EpochTown): {
    summary: GenerationSummary
    legacyTraits: LegacyTrait[]
    inheritancePreview: string[]
    recommendations: string[]
  } {
    const summary = this.archiveTownGeneration(town)
    const inheritancePreview = this.determineInheritedTraits(town.genetics)
    
    const recommendations: string[] = []
    
    // Generate recommendations for future generations
    if (town.genetics.legacyTraits.length < 3) {
      recommendations.push('Focus on developing stronger community traits to create more legacy benefits')
    }
    
    if (town.stats.totalContributions < 500) {
      recommendations.push('Encourage more citizen participation to strengthen genetic inheritance')
    }
    
    if (inheritancePreview.length === 0) {
      recommendations.push('Work on achieving milestone requirements to unlock inheritable traits')
    }

    return {
      summary,
      legacyTraits: town.genetics.legacyTraits,
      inheritancePreview,
      recommendations
    }
  }
}

export default LegacyManager
